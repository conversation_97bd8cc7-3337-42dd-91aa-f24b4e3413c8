# Task ID: 5
# Title: Implement PRD Generation Mode Detection
# Status: done
# Dependencies: None
# Priority: medium
# Description: Implement the Main Hierarchical Cascade Flow for PRD generation mode detection and user input validation. Replace the former multi-agent approach with a sequential, gate-based system that evaluates user messages through four validation gates and short-circuits on first failure.
# Details:
Build a sequential gate pipeline using Convex TypeScript functions:
1. Vagueness Gate – FAILS when technical jargon or acronyms appear without context. Generates a clarifying question requesting layman wording or added context.
2. Focus Gate – FAILS when the request mixes multiple products or disparate ideas. Responds asking the user to pick a single product focus.
3. Scope Gate – FAILS when feature lists extend beyond a minimal lovable product (MLP). Pushes back on feature creep and requests trimmed scope.
4. Specificity Check – Runs a refinement loop (max 3 iterations) that ensures concrete details on problem, core feature, target user, and success metrics. If still inadequate after the loop, returns a final failure message; otherwise PASS.

Implementation requirements:
• Each gate is a pure Convex function that returns { pass: boolean; message?: string }.
• mainCascadeFlow mutation invokes gates in order and exits early on first failure, storing the gate result and conversation state in Convex DB.
• Successful passage of all gates flips the conversation state flag `mode = "generation"`, triggering PRD generation.
• Client-side React component subscribes to conversation state via reactive Convex query and surfaces gate failure messages or shows the "Generate PRD" button when mode === "generation".
• Remove/deprecate any prior multi-agent logic, ensuring backward compatibility by routing old entry points to the new cascade.


# Test Strategy:
Unit-test each gate with representative PASS and FAIL inputs using Convex test harness. Verify early-exit short-circuiting by injecting failing input at each gate and asserting downstream gates are skipped. Test refinement loop for Specificity Gate with 0–3 iterations and ensure mode remains "clarification" until PASS. Integration tests: send complete conversation flows and confirm (a) state transitions, (b) correct failure messages, (c) final PRD generation includes earlier clarified details. Regression tests ensure legacy endpoints now call mainCascadeFlow without breaking existing functionality.

# Subtasks:
## 1. Define GateResult types and shared interfaces [done]
### Dependencies: None
### Description: 
### Details:
<info added on 2025-07-27T18:50:23.923Z>
Port the skeleton workflow code into the production Convex tree and align it with the new GateResult interfaces:

• Copy docs/convex/agents/workflows/example/cascade-workflow.ts → convex/workflows/cascadeWorkflow.ts  
• Copy gate files (vaguenessGate.ts, focusGate.ts, scopeGate.ts, refinementGate.ts, responseGen.ts) → convex/workflows/gates/

Adaptation checklist  
1. Replace placeholder imports with shared interfaces defined in this subtask (`GateResult`, `GateContext`, `GateFailureReason`).  
2. Convert each gate file to export a Convex action (`export const vaguenessGate = action({...})`) or query as appropriate; remove any OpenAI-specific stubs.  
3. Update import paths to use the project alias/relative paths (`import { GateResult } from "convex/workflows/sharedTypes"`).  
4. Refactor database interactions to use Convex `db` from the action context; strip out mock storage layers.  
5. Review schema.ts – add/extend tables or enums referenced by the gates (e.g., `prd_ideas`, `clarifications`, `GateStatusEnum`).  
6. Ensure cascadeWorkflow sequentially awaits each gate and short-circuits on first failure; propagate GateResult back to caller.  
7. Delete redundant helper utilities already implemented elsewhere (e.g., duplicate zod validators).  
8. Add missing dev/runtime deps (zod, lodash) to package.json and run `pnpm install`.  
9. Run `pnpm type-check && pnpm test` to confirm no type or unit-test regressions; write a minimal vitest suite proving pass/fail flows compile.
</info added on 2025-07-27T18:50:23.923Z>
<info added on 2025-07-27T20:27:21.487Z>
Progress update – core implementation completed:

• Added convex/workflows/sharedTypes.ts with comprehensive type definitions (GateResult, DetailedGateResult, GateContext, GateFailureReason, CascadeFlowResult) and associated validators/config constants.  
• Ported cascadeWorkflow orchestrator plus individual gate functions (vaguenessGate, focusGate, scopeGate, specificityGate) into convex/workflows/gates/, removing OpenAI-specific stubs and adapting to Convex action context.  
• Introduced gate_results table and indexes in schema.ts for persisting execution outcomes.  
• Replaced workflow mutations with direct function calls inside the cascade flow, preserving error handling and database writes.  
• Implemented regex/heuristic detection logic with configurable confidence thresholds and detailed failure categorization per gate.  
• Codebase compiles cleanly, passes linting, and is ready for integration tests.
</info added on 2025-07-27T20:27:21.487Z>

## 2. Implement Vagueness Gate Convex function [done]
### Dependencies: None
### Description: 
### Details:
<info added on 2025-07-27T18:51:36.563Z>
Implementation Details (updated)

1. File structure  
   • Rename `vagueness-gate-agent.ts` ➝ `convex/gates/vaguenessGate.ts` and convert to a Convex `mutation` so it can both read and persist data.  
   • Export signature:  
     ```
     export const vaguenessGate = mutation({
       args: { conversationId: v.id("conversations"), message: v.string() },
       handler: async (ctx, { conversationId, message }) => { … }
     });
     ```

2. Detection logic refinements  
   Replace the brittle keyword list with pattern buckets that mirror the PRD analysis rubric:  
   a. Technical jargon / acronyms without context  
      – Regex: `\b(API|SDK|ML|AI|OCR|ETL|LLM|SaaS)\b(?![^()]{0,40}\()`  
   b. Solution-first language (immediately proposes features without stating a problem)  
      – Heuristic: sentence starts with a verb phrase (“Build”, “Create”, “Develop”, “Implement”) before any “problem” synonyms (“pain”, “issue”, “challenge”, “struggle”) appear.  
   c. Architecture buzzwords without accompanying problem statement  
      – Regex match on `\b(microservice|serverless|event[- ]driven|dockerized|Kubernetes)\b` with same problem-term distance check as above.  
   For each match store `{patternId, snippet}` in `failReasons`.

3. Convex schema integration  
   • Extend `gate_results` table:  
     ```
     id: v.id("gate_results")
     conversationId: v.id("conversations")
     gate: v.literal("vagueness")
     status: v.union(v.literal("pass"), v.literal("fail"))
     reasons: v.optional(v.array(v.string()))
     createdAt: v.number()
     ```  
   • On mutation success write a row; on DB error throw `new Error("DB_WRITE_FAILED")` to be caught by the pipeline runner.

4. Response contract  
   ```
   type VaguenessGateResult =
     | { status: "pass" }
     | { status: "fail"; followUp: string; reasons: string[] };
   ```  
   • Generate `followUp` using `lib/prompts.generateClarification(reasons)` which wraps reasons in a single ask-for-clarity sentence.  
   • Upstream caller short-circuits when `status === "fail"`.

5. Error handling  
   • Wrap pattern evaluation and DB write in try/catch; return `{ status:"fail", followUp:"Internal error. Please try again." }` if unexpected exceptions occur, while logging the stack with `ctx.log.error`.  
   • Distinguish between user-facing failures (vagueness) and system errors (500).

6. Tests  
   • Unit tests in `convex/gates/__tests__/vaguenessGate.test.ts` covering:  
     – PASS: “Users forget passwords too often…” (no jargon).  
     – FAIL-jargon: “We need an AI SaaS that uses LLMs…”  
     – FAIL-solution: “Build a dashboard that shows metrics…”  
     – DB write failure simulation with `jest.spyOn(ctx.db, "insert").mockRejectedValue(...)`.

Deliverables  
• Updated TypeScript file, schema patch, unit tests passing.
</info added on 2025-07-27T18:51:36.563Z>

## 3. Implement Focus Gate Convex function [done]
### Dependencies: None
### Description: 
### Details:
<info added on 2025-07-27T18:53:40.706Z>
• Refactor `focus-gate-agent.ts` into a Convex server function `/convex/gates/focusGate.ts`, exporting `v1/focus_gate` that receives `{text: string}` and returns `{pass: boolean, msg?: string, clusters?: string[]}`.  
• Port existing regex heuristics and expand them to production-grade multi-product detection:  
  – Identify ≥2 distinct noun phrases preceded by capitalisation, commas, “and”, “or”, “plus”, “/”.  
  – Detect pattern `<platform> AND <tool>` or `<mobile|web> app + <browser extension|AI assistant>` indicating composite products.  
  – Flag explicit enumerations (bullet-like lists, semicolon separated items).  
• Integrate with Convex schema `gate_logs` (already used by other gates): insert log row `{gate:"focus", text, pass, reason}`.  
• On `pass === false` build a binary forcing response:  
  1. Extract top two product clusters (via simple noun-phrase frequency).  
  2. Return `msg` like: “Your request mentions both ‘{clusterA}’ and ‘{clusterB}’. Which single product would you like to focus on? Reply ‘A’ for {clusterA} or ‘B’ for {clusterB}.” and include `clusters:["A","B"]`.  
• Update type definitions in `/convex/schema.ts` and `types/gates.ts`.  
• Add unit tests in `/test/focusGate.test.ts` covering: single product (PASS), multi product with “and”, comma list, platform+tool combo, bullet list, and edge cases with conjunctions inside a single idea.  
• Ensure gate short-circuits: export `focusGate()` and compose in the pipeline so downstream gates only run when `pass === true`.
</info added on 2025-07-27T18:53:40.706Z>
<info added on 2025-07-27T22:02:23.175Z>
• Scale back testing scope: create a minimal sanity suite at `convex/workflows/gates/__tests__/focusGate.test.ts` with only 2–3 assertions—(1) single-product input returns `{pass:true}`, (2) multi-product input containing “and” returns `{pass:false}`, and (3) one representative edge case validating heuristics; comprehensive coverage will be provided later in subtask 5.9.
</info added on 2025-07-27T22:02:23.175Z>
<info added on 2025-07-27T22:58:48.553Z>
• Core implementation finished and merged: `focusGateInternal` pure function plus Convex mutation `focusGate({conversationId, message}) → DetailedGateResult`, persisting results to `gate_results`.  
• Production-grade multi-product detection in place (comparatives, enumerations, domain mixing, platform combos) with confidence thresholds and reason codes.  
• Binary A/B clarification message auto-generated from top two noun-phrase clusters on failure.  
• Added Focus Gate entry to `GATE_CONFIGS` and execution branch in `cascadeWorkflow.ts`; downstream gates are bypassed when `pass === false`.  
• Error-safe logging and uniform return schema aligned with other gates; both pass/fail outcomes recorded.  
• Minimal sanity test suite (`__tests__/focusGate.test.ts`) implemented with three assertions (single product pass, “and” failure, comparison failure) — all green.  
• Subtask implementation complete; ready for hand-off to Scope Gate (5.4).
</info added on 2025-07-27T22:58:48.553Z>

## 4. Implement Scope Gate Convex function [done]
### Dependencies: None
### Description: 
### Details:
<info added on 2025-07-27T18:54:15.242Z>
Implementation requirements for production-ready Scope Gate:

• Replace scope-gate-agent.ts proof-of-concept with a Convex action/function (`scopeGate.ts`) that:
  – Accepts the parsed PRD analysis object and user prompt as input.
  – Flags “scope creep” when any of the following rules are true:
      1. features.length > 5
      2. >1 distinct core_function found in analysis.coreFunctions array
      3. prompt or analysis.summary contains ecosystem-scale cues (e.g. “platform”, “marketplace”, “app store”, “plugin system”, “API for others”, “ecosystem”)

• Persist evaluation result to `prd_gate_results` table (schema already used by Vagueness & Focus gates) with fields:
  id, prdId, gate: "scope", passed: boolean, reason: string[], createdAt.

• On FAIL, return an assistant response generated via Claude with the template:
  “Your current idea risks becoming a kitchen-sink product because: {{reasons}}.
   Let’s narrow to a Minimal Lovable Product. Pick ONE primary user scenario and up to 3 must-have features.”

• Ensure the function short-circuits the downstream pipeline by throwing `GateFailError` identical to other gates.

• Unit tests:
  – PASS example: 3 features, single core function, no ecosystem wording.
  – FAIL examples for each rule above, verify stored reasons array and response copy.
  – Integration test confirms pipeline halts and Specificity gate is skipped on FAIL.

• Performance: Max execution 300 ms, memory < 20 MB; move all regex/string checks in-memory, no external calls except Claude on failure.

• Update documentation in `/docs/gates.md` with rule set and response template.
</info added on 2025-07-27T18:54:15.242Z>
<info added on 2025-07-27T22:02:42.290Z>
Revised testing scope:

• Replace the previous exhaustive test suite with a lightweight smoke test file at `convex/workflows/gates/__tests__/clarityGate.test.ts`.
• Implement only 2–3 assertions:
  – Clear, well-formed input → expect PASS and `passed === true`.
  – Unclear or ambiguous input → expect FAIL, populated `reason` array, and thrown `GateFailError`.
  – One edge case (e.g., empty `features` array but ecosystem wording present) to confirm rule evaluation accuracy.
• No integration tests or rule-by-rule permutations are required here; comprehensive coverage will be delivered in subtask 5.9.
</info added on 2025-07-27T22:02:42.290Z>

## 5. Implement Specificity Check with refinement loop [done]
### Dependencies: None
### Description: 
### Details:
<info added on 2025-07-27T18:54:50.534Z>
Implementation details  
• Refactor /lib/agents/refinement-agent.ts → /convex/gates/specificityGate.ts and swap the OpenAI call for the existing Claude 3.5 helper in /lib/llm.ts.  
• Gate signature  
  export const specificityGate = mutation(({db}, {msg, iteration}:{msg:string,iteration:number}) => Promise<GateResult>)  
  where GateResult = {status:'PASS'|'ASK'|'LOCK_FAIL', question?:string, iteration:number}.  
• Specificity heuristic (all must be satisfied to PASS)  
  1. Explicit target user/persona (regex `(for|to)\s+(?:busy|early|solo|small|enterprise|[a-z]+)\s+(?:founders?|marketers?|developers?)`).  
  2. Singular core feature statement detected by “the core feature is” or one bullet.  
  3. Measurable success metric (contains % / $, “KPI”, “metric”, or quantifier).  
  4. Problem statement < 160 chars starting with verb (“struggle”, “need”, “cannot”).  
• On failure the agent returns status ASK and a single clarification question chosen from a question bank keyed to the missing item.  
• mainCascadeFlow will re-invoke the gate until status PASS or iteration === 3.  
• If iteration hits 3 without PASS return status LOCK_FAIL so downstream logic can either halt or fall back to manual triage.  
• Store iteration count and lastQuestion in “refinement_sessions” table keyed by chatId to persist across requests.  
• Maximum runtime 4s; set Claude temperature 0.3 for determinism.  
• Unit-test three trajectories: immediate PASS, PASS on 2nd iteration, LOCK_FAIL after 3.
</info added on 2025-07-27T18:54:50.534Z>
<info added on 2025-07-27T22:03:02.331Z>
Revised test scope  
• Supersede the earlier “three-trajectory” plan with a lightweight smoke-test suite.  
• Add `convex/workflows/gates/__tests__/specificityGate.test.ts` containing 2-3 cases only:  
  – happy-path input that satisfies all heuristics → expect status ‘PASS’.  
  – generic / vague input that misses at least one heuristic → expect status ‘ASK’.  
  – one edge case (e.g., empty string or missing iteration) → expect graceful ‘LOCK_FAIL’ or typed error.  
• Purpose is dependency verification and basic correctness; exhaustive scenarios will be implemented in task 5.9.
</info added on 2025-07-27T22:03:02.331Z>

## 6. Create mainCascadeFlow mutation orchestrating gates with early exit [done]
### Dependencies: None
### Description: 
### Details:
<info added on 2025-07-27T18:55:19.282Z>
Objective:
Turn the experimental cascade-workflow.ts prototype into a production-ready Convex mutation named mainCascadeFlow that drives the four validation gates, performs short-circuiting on first failure, and persists all results to the conversation state tables.

Implementation steps:
1. File location: /convex/mainCascadeFlow.ts (export default async function mainCascadeFlow(ctx, {conversationId, userMessage}: {conversationId: Id<"conversations">; userMessage: string}))
2. Import individual gate helpers (vaguenessGate, focusGate, scopeGate, specificityGate) from /convex/gates/.
3. Retrieve current conversation object and prior messages with ctx.db.query("messages").withIndex("by_conversation", conversationId)… .
4. Build a gatePipeline array of functions in execution order; iterate with for-of:
   a. Call gate(userMessage, priorMessages).
   b. If result.status === "fail", write a new assistant message containing result.clarificationPrompt, update conversation.mode to "clarification", and return {mode: "clarification"} early.
5. If all gates pass, set conversation.mode = "prdGeneration", append a system message “All gates passed – proceeding to PRD generation”, and return {mode: "prdGeneration"}.
6. Ensure atomicity with ctx.db.patch / ctx.db.insert inside a single mutation.
7. Remove legacy multi-agent flow references; mark old fields deprecated.
8. Export types GateResult, GateStatus for downstream consumers (UI and tests).

Testing / acceptance criteria:
• Unit tests using convex/dev harness feed representative inputs that fail each gate; assert mainCascadeFlow stops exactly at first failure and writes only one assistant clarification message.
• Happy-path test where all gates pass verifies conversation.mode flips to "prdGeneration" and no extra messages are written.
• Regression test confirms conversation state remains consistent after concurrent requests (use convex testing’s ctx.scheduler.runConcurrent).
• Lint, type-check, and ensure 100% branch coverage on gate branching logic.
</info added on 2025-07-27T18:55:19.282Z>
<info added on 2025-07-27T22:03:23.474Z>
Replace the previous comprehensive gate-coverage requirements with the following minimal testing scope:

• Add a single smoke-test file at `convex/workflows/gates/__tests__/feasibilityGate.test.ts` containing 2–3 basic assertions: one “feasible” input that returns PASS, one “infeasible” input that returns FAIL, and one edge-case input to confirm correct status handling. These tests only verify correct imports and expected GateResult values; exhaustive scenarios and branch coverage will be delivered in task 5.9.
</info added on 2025-07-27T22:03:23.474Z>

## 7. Integrate cascade flow into existing conversation state management and remove old multi-agent logic [done]
### Dependencies: None
### Description: 
### Details:
<info added on 2025-07-27T18:55:56.383Z>
Implementation outline for cascade integration and cleanup:

• Schema update: add fields to `conversations` table –  
  • `gateStates?: { [gate: string]: 'PASS' | 'FAIL' | 'PENDING' }`  
  • `cascadeVersion?: number` (default 1) – with migration script that back-fills existing records with empty `gateStates` and version 1.

• State helpers: create `setGateState(convId, gate, state)` and `getGateStates(convId)` utilities in `/convex/flows/cascadeState.ts` for consistent read/write access across mutations.

• Refactor logic: replace all imports of `multiAgentController` with `executeCascadeFlow` inside `mainCascadeFlow` mutation; remove `agentPromptBuilder`, `agentTaskQueue`, and any `Agent*` types. Delete `agents/` directory and purge dead code from barrel exports.

• Route adaptation: in `/app/api/chat/route.ts` append latest `gateStates` snapshot to the assistant message’s `metadata` field. Maintain existing message shape so the chat UI renders unchanged when `metadata.gateStates` is undefined.

• Client compatibility: update `useChat` hook mapper to ignore unknown `metadata` keys, and guard new gate UI components behind `if (metadata?.gateStates)`.

• Test migration:  
  1. Port `multiAgent` unit tests to `cascade` equivalents with pass/fail fixtures.  
  2. Add regression test ensuring old conversations (no `gateStates`) still render and accept messages.  

• Documentation: update `/docs/architecture.md` and public types (`/types/chat.ts`) to describe hierarchical cascade system and new schema fields.

Deliverables: schema migration, refactored mutations, removed agent code, updated API route & client guards, passing test suite, docs.
</info added on 2025-07-27T18:55:56.383Z>
<info added on 2025-07-27T22:03:53.304Z>
Revise the testing scope for this subtask:

• Replace the previous “Test migration” bullet with a lightweight smoke-test requirement.  
• Create a single test file `convex/workflows/gates/__tests__/mainCascadeFlow.test.ts` containing only 2–3 basic cases:  
  1. All gates PASS → success flow reaches the end.  
  2. First gate FAILS → early exit with no downstream gate execution.  
  3. One simple edge case (e.g., empty user input) verifying graceful handling.  
• Purpose is limited to ensuring correct imports, cascade orchestration, and state mutation wiring; comprehensive coverage and legacy-conversation regressions are deferred to Task 5.9.  
• Remove plans to port the full multi-agent test suite in this subtask.
</info added on 2025-07-27T22:03:53.304Z>

## 8. Update client-side components to display gate feedback and show 'Generate PRD' button on PASS [done]
### Dependencies: None
### Description: 
### Details:
<info added on 2025-07-27T18:56:40.871Z>
Implementation details:
• Create a shared Convex table `gate_runs` keyed by conversation id that stores current gate index, status (pending | pass | fail), and failureMessage.  
• Expose a `listGateStates` query returning ordered gate objects and a `currentMode` field (“clarification” | “generation”).  
• Build three new client-side React components in `/components/cascade`:
  1. `GateProgressBar.tsx` – horizontal stepper that renders the four gates with icons/colors for pending (grey), pass (green) and fail (red). Consumes `useQuery(api.gates.listGateStates, {conversationId})`.  
  2. `GateFeedback.tsx` – conditional alert shown below the chat input when the latest gate status === “fail”. Renders `failureMessage` and CTA text like “Please clarify and resend”.  
  3. `GeneratePrdButton.tsx` – primary button surfaced when `currentMode === "generation"` AND every gate state === “pass”. On click it posts a “Generate PRD” assistant trigger message to the chat stream.

UI changes:
• Mount `GateProgressBar` at top of `/chat/page.tsx` under the conversation header.  
• Mount `GateFeedback` just above the message list so failures are immediately visible.  
• Place `GeneratePrdButton` within the chat input toolbar; hide it in clarification mode.

UX rules:
• While a gate is evaluating, show a small indeterminate progress indicator beside that gate’s name.  
• On any failure, freeze subsequent gates to “pending” until the user sends another message.  
• Auto-scroll to the bottom when the button injects the “Generate PRD” command.

Acceptance criteria:
1. Progress bar updates in real time without manual refresh.  
2. Correct failure message displayed for each gate’s specific rejection.  
3. Button only appears after all four gates pass and disappears if a later user message fails a gate.  
4. Clicking button triggers mode switch and server receives “generate_prd” command.

Test strategy:
• Mock gate states in Storybook to snapshot PASS, FAIL, and pending states.  
• Cypress e2e: send messages that intentionally fail each gate and assert UI responses.  
• Unit test `GeneratePrdButton` logic to ensure correct visibility based on query values.
</info added on 2025-07-27T18:56:40.871Z>
<info added on 2025-07-27T22:04:15.616Z>
Testing scope adjustment:
• Implement a lightweight test file `convex/workflows/gates/__tests__/cascadeIntegration.test.ts` with 2–3 assertions that verify (1) a complete PASS flow, (2) error handling when a gate rejects, and (3) one edge condition such as an unexpected `undefined` gate state. These smoke tests exist solely to confirm wiring and imports; exhaustive scenarios will be covered in subtask 5.9.
</info added on 2025-07-27T22:04:15.616Z>

## 9. Write unit tests for individual gate functions [done]
### Dependencies: None
### Description: 
### Details:
<info added on 2025-07-27T18:57:55.050Z>
Write a full Convex test suite that exercises every gate function in isolation:

1. Test scenarios  
   • PASS-case: supply at least three representative inputs per gate that should succeed and confirm the function returns `{status: 'PASS', message: expect.any(String)}`.  
   • FAIL-case: supply at least three representative inputs per gate that should fail and confirm returned clarifying message matches the gate’s specification.  
   • Edge/boundary inputs: empty string, extremely long strings (>10 k chars), malformed JSON payloads, and repeated whitespace.  
   • Database interaction: mock `db` object to assert that gate feedback is written with correct `gateName`, `status`, and `userId` for both PASS and FAIL paths; verify no extraneous writes occur.  
   • Error handling: force underlying helper utilities to throw and assert the gate returns `{status:'ERROR'}` and logs an exception event.  

2. Framework integration  
   • Use `convex test` with the built-in Jest environment; no external test runner.  
   • Place test files under `tests/gates/*.test.ts`.  
   • Import gates via `import {vaguenessGate, focusGate, scopeGate, specificityGate} from '../../../convex/gates';` and inject a mocked `actionCtx`.  
   • Leverage Convex’s `internalMutation` mock helpers to spy on database writes.

3. Coverage targets  
   • Achieve ≥95 % statement and branch coverage across `convex/gates.ts`.  
   • Include coverage thresholds in `package.json` test script; CI must fail if unmet.

4. Deliverables  
   • Test files committed and passing with `pnpm test`.  
   • Updated README section “Gate Testing” describing how to run and interpret the suite.
</info added on 2025-07-27T18:57:55.050Z>

## 10. Write integration tests for full cascade and state transitions [done]
### Dependencies: None
### Description: 
### Details:
<info added on 2025-07-27T18:58:21.354Z>
Create a comprehensive integration-test suite that drives the entire gate cascade from the chat entry point and asserts end-to-end behaviour.

Scope
• Exercise full user journeys that (a) succeed through all four gates, (b) fail at each individual gate, and (c) loop through the Specificity refinement cycle 0-3 times before succeeding.  
• Assert state flags switch correctly between “clarification” and “generation” modes on:  
  – first successful pass of all gates after a “Generate PRD” command  
  – fallback to clarification when a new ambiguous message arrives after PRD output.  
• Verify that when a gate fails, downstream gates are skipped and the correct clarifying question is returned to the chat UI.  
• Inject artificial errors (e.g. thrown exception inside a gate) and confirm graceful error message, no state corruption, and ability to continue the conversation afterward.  
• Confirm integration with the existing chat interface: messages appear in proper order, streaming works, and assistant role tags are preserved.  
• Validate that rate-limiting headers (from Task 7) and other shared middleware are not broken by the cascade.

Implementation Notes
• Use Convex test harness for server functions plus Playwright component tests for the `/app/chat` page to capture real UI output.  
• Provide helper fixtures that build mock conversation transcripts and assert final Convex mutation results plus rendered DOM text.  
• Aim for ≥90 % branch coverage across `cascade.ts`, gate functions, and chat API route.

Acceptance Criteria
All tests pass in CI, coverage thresholds are met, and failures in any part of the cascade (logic or UI) are surfaced with clear assertions.
</info added on 2025-07-27T18:58:21.354Z>

